import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, MoreHori<PERSON>tal, Pencil, Trash2 } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LeaveTypeForm } from "@/components/forms/leave-type-form";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface LeaveTypesTabProps {
  leaveTypes: any[];
  loading: boolean;
  onAdd: () => void;
  onEdit: (leaveType: any) => void;
  onDelete: (leaveType: any) => void;
}

export function LeaveTypesTab({
  leaveTypes,
  loading,
  onAdd,
  onEdit,
  onDelete
}: LeaveTypesTabProps) {
  // Log leave types untuk debugging
  console.log('LeaveTypesTab received leaveTypes:', leaveTypes);
  const [isAddLeaveTypeOpen, setIsAddLeaveTypeOpen] = useState(false);
  const [isEditLeaveTypeOpen, setIsEditLeaveTypeOpen] = useState(false);
  const [selectedLeaveType, setSelectedLeaveType] = useState<any>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);

  const handleAddClick = () => {
    console.log('Opening leave type add dialog');

    // Force cleanup any existing modal state
    setIsAddLeaveTypeOpen(false);
    setSelectedLeaveType(null);

    // Small delay to ensure cleanup, then open
    setTimeout(() => {
      setIsAddLeaveTypeOpen(true);
      console.log('Leave type add opened');
    }, 50);
  };

  const handleEditClick = (leaveType: any) => {
    console.log('Opening leave type edit for:', leaveType);

    // Force cleanup any existing modal state
    setIsEditLeaveTypeOpen(false);
    setSelectedLeaveType(null);

    // Small delay to ensure cleanup, then open
    setTimeout(() => {
      setSelectedLeaveType(leaveType);
      setIsEditLeaveTypeOpen(true);
      console.log('Leave type edit opened');
    }, 50);
  };

  const handleDeleteClick = (leaveType: any) => {
    console.log('Opening leave type delete for:', leaveType);

    // Force cleanup any existing modal state
    setIsDeleteConfirmOpen(false);
    setSelectedLeaveType(null);

    // Small delay to ensure cleanup, then open
    setTimeout(() => {
      setSelectedLeaveType(leaveType);
      setIsDeleteConfirmOpen(true);
      console.log('Leave type delete opened');
    }, 50);
  };

  const handleConfirmDelete = () => {
    if (selectedLeaveType) {
      onDelete(selectedLeaveType);
      setIsDeleteConfirmOpen(false);
    }
  };

  return (
    <div className="bg-card rounded-lg shadow-sm p-6">
      <div className="bg-card text-card-foreground rounded-lg shadow-md overflow-hidden">
        <div className="p-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 border-b border-border">
          <div>
            <h2 className="text-lg font-semibold">Leave Types</h2>
            <p className="text-xs text-muted-foreground">Manage leave types for your organization</p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button onClick={handleAddClick} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add New
            </Button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Days Allowed</TableHead>
                <TableHead>Description</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-4">
                    <div className="flex justify-center items-center space-x-2">
                      <div className="animate-spin h-4 w-4 border-2 border-primary rounded-full border-t-transparent"></div>
                      <span>Loading leave types...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : leaveTypes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-4">
                    No leave types found
                  </TableCell>
                </TableRow>
              ) : (
                leaveTypes.map((leaveType) => (
                  <TableRow key={leaveType.id}>
                    <TableCell>{leaveType.id}</TableCell>
                    <TableCell>{leaveType.name}</TableCell>
                    <TableCell>{leaveType.daysAllowed}</TableCell>
                    <TableCell>{leaveType.description || '-'}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0" size="mobile">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditClick(leaveType)}>
                            <Pencil className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDeleteClick(leaveType)} className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Dialog untuk menambah leave type */}
      <Dialog
        key={`leave-type-add-${Date.now()}`}
        open={isAddLeaveTypeOpen}
        onOpenChange={(open) => {
          console.log('Leave type add dialog state changed:', open);
          if (!open) {
            console.log('Closing leave type add dialog - starting cleanup');

            // Immediate cleanup
            setIsAddLeaveTypeOpen(false);
            setSelectedLeaveType(null);

            // Force DOM cleanup
            setTimeout(() => {
              console.log('Leave type add dialog cleanup completed');

              // Remove any lingering modal elements
              const modals = document.querySelectorAll('[data-radix-dialog-overlay]');
              modals.forEach(modal => modal.remove());

              // Reset body styles
              document.body.style.pointerEvents = 'auto';
              document.body.style.overflow = 'auto';
              document.body.classList.remove('overflow-hidden');

              // Force focus back to body
              document.body.focus();

              console.log('DOM cleanup completed for leave type add');
            }, 150);
          } else {
            setIsAddLeaveTypeOpen(open);
          }
        }}
        modal={true}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Leave Type</DialogTitle>
          </DialogHeader>
          <LeaveTypeForm
            onSubmit={(data) => {
              // Panggil onAdd dengan data form
              onAdd(data);
              // Tutup dialog
              setIsAddLeaveTypeOpen(false);
            }}
            onCancel={() => setIsAddLeaveTypeOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Dialog untuk mengedit leave type */}
      <Dialog
        key={`leave-type-edit-${selectedLeaveType?.id || 'none'}`}
        open={isEditLeaveTypeOpen}
        onOpenChange={(open) => {
          console.log('Leave type edit dialog state changed:', open);
          if (!open) {
            console.log('Closing leave type edit dialog - starting cleanup');

            // Immediate cleanup
            setIsEditLeaveTypeOpen(false);
            setSelectedLeaveType(null);

            // Force DOM cleanup
            setTimeout(() => {
              console.log('Leave type edit dialog cleanup completed');

              // Remove any lingering modal elements
              const modals = document.querySelectorAll('[data-radix-dialog-overlay]');
              modals.forEach(modal => modal.remove());

              // Reset body styles
              document.body.style.pointerEvents = 'auto';
              document.body.style.overflow = 'auto';
              document.body.classList.remove('overflow-hidden');

              // Force focus back to body
              document.body.focus();

              console.log('DOM cleanup completed for leave type edit');
            }, 150);
          } else {
            setIsEditLeaveTypeOpen(open);
          }
        }}
        modal={true}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Leave Type</DialogTitle>
          </DialogHeader>
          {selectedLeaveType && (
            <LeaveTypeForm
              initialData={selectedLeaveType}
              onSubmit={(data) => {
                // Pastikan data yang dikirim memiliki ID dari selectedLeaveType
                const updatedData = {
                  id: selectedLeaveType.id,
                  name: data.name,
                  description: data.description || '',
                  daysAllowed: data.daysAllowed,
                  requiresApproval: data.requiresApproval
                };
                console.log('Sending data to edit:', updatedData);
                onEdit(updatedData);
                setIsEditLeaveTypeOpen(false);
              }}
              onCancel={() => setIsEditLeaveTypeOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Dialog konfirmasi hapus */}
      <Dialog
        key={`leave-type-delete-${selectedLeaveType?.id || 'none'}`}
        open={isDeleteConfirmOpen}
        onOpenChange={(open) => {
          console.log('Leave type delete dialog state changed:', open);
          if (!open) {
            console.log('Closing leave type delete dialog - starting cleanup');

            // Immediate cleanup
            setIsDeleteConfirmOpen(false);
            setSelectedLeaveType(null);

            // Force DOM cleanup
            setTimeout(() => {
              console.log('Leave type delete dialog cleanup completed');

              // Remove any lingering modal elements
              const modals = document.querySelectorAll('[data-radix-dialog-overlay]');
              modals.forEach(modal => modal.remove());

              // Reset body styles
              document.body.style.pointerEvents = 'auto';
              document.body.style.overflow = 'auto';
              document.body.classList.remove('overflow-hidden');

              // Force focus back to body
              document.body.focus();

              console.log('DOM cleanup completed for leave type delete');
            }, 150);
          } else {
            setIsDeleteConfirmOpen(open);
          }
        }}
        modal={true}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete this leave type?</p>
            <p className="text-sm text-muted-foreground mt-2">
              This action cannot be undone. All leave requests associated with this type will be affected.
            </p>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsDeleteConfirmOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete}>
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
