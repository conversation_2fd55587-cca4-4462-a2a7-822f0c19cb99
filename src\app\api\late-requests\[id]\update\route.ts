import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { cookies } from 'next/headers';



export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('user');

    if (!userCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userData = JSON.parse(userCookie.value);
    const userRole = userData.role;
    // Get ID from params
    const id = params.id;
    const data = await request.json();
    const { status, approvedById, lateType, lateDate, estimatedTime, reason, attachmentUrl } = data;

    // Check if late request exists
    const lateRequest = await prisma.lateRequest.findUnique({
      where: { id: parseInt(id) },
      include: {
        employee: {
          include: {
            department: {
              include: {
                head: true
              }
            }
          }
        }
      }
    });

    if (!lateRequest) {
      return NextResponse.json({ error: 'Late request not found' }, { status: 404 });
    }

    // Cek apakah ini adalah operasi approve/reject atau edit
    const isStatusUpdate = status && (status === 'approved' || status === 'rejected');
    const isEdit = lateType || lateDate || estimatedTime || reason || attachmentUrl !== undefined;

    // Jika ini adalah operasi approve/reject
    if (isStatusUpdate) {
      // Validasi: Hanya admin, supervisor, dan HEAD yang dapat approve/reject
      if (userRole !== 'ADMIN' && userRole !== 'SUPERVISOR' && userRole !== 'HEAD') {
        return NextResponse.json(
          { error: 'Only admin, supervisor, and head can approve or reject late requests' },
          { status: 403 }
        );
      }

      // Supervisor dan HEAD tidak dapat approve/reject late request miliknya sendiri
      const isOwnRequest = lateRequest.employeeId === parseInt(userData.id);
      if ((userRole === 'SUPERVISOR' || userRole === 'HEAD') && isOwnRequest) {
        return NextResponse.json(
          { error: 'You cannot approve/reject your own late requests' },
          { status: 403 }
        );
      }

      // Supervisor hanya dapat approve/reject late request dari bawahannya
      if (userRole === 'SUPERVISOR') {
        // Gunakan employeeId jika ada, jika tidak gunakan id
        const supervisorId = userData.employeeId ? userData.employeeId : userData.id;

        // Cek apakah supervisor adalah head dari departemen karyawan
        const isSupervisorOfEmployee =
          lateRequest.employee?.department?.head?.id === parseInt(supervisorId) ||
          lateRequest.employee?.department?.head?.employeeId === supervisorId;

        if (!isSupervisorOfEmployee) {
          return NextResponse.json(
            { error: 'You can only approve/reject late requests from your subordinates' },
            { status: 403 }
          );
        }
      }

      // HEAD hanya dapat approve/reject late request dari karyawan dengan posisi Chief atau Kepala
      else if (userRole === 'HEAD') {
        // Ambil data posisi karyawan
        const employeeWithPosition = await prisma.employee.findUnique({
          where: { id: lateRequest.employeeId },
          include: { position: true }
        });

        const positionTitle = employeeWithPosition?.position?.title || '';
        const isChiefOrKepalaOrPsikolog =
          positionTitle.toLowerCase().includes('chief') ||
          positionTitle.toLowerCase().includes('kepala') ||
          positionTitle.toLowerCase().includes('psikolog');

        if (!isChiefOrKepalaOrPsikolog) {
          console.error(`PUT /api/late-requests/${id}/update - Forbidden: Employee is not a Chief, Kepala, or Psikolog`);
          return NextResponse.json(
            { error: 'You can only approve/reject late requests from Chief, Kepala, or Psikolog positions' },
            { status: 403 }
          );
        }
      }
    }
    // Jika ini adalah operasi edit
    else if (isEdit) {
      // Validasi: Hanya admin atau pemilik request yang dapat mengedit
      if (userRole !== 'ADMIN' && lateRequest.employeeId !== parseInt(userData.id)) {
        return NextResponse.json(
          { error: 'You can only edit your own late requests' },
          { status: 403 }
        );
      }

      // Validasi: Hanya request dengan status pending yang dapat diedit
      if (lateRequest.status !== 'pending') {
        return NextResponse.json(
          { error: 'Only pending late requests can be edited' },
          { status: 400 }
        );
      }
    }

    // Siapkan data untuk update
    const updateData: any = {};

    // Jika ini adalah operasi approve/reject
    if (isStatusUpdate) {
      updateData.status = status;
      updateData.approvedById = parseInt(approvedById);
      updateData.approvedAt = new Date();
    }
    // Jika ini adalah operasi edit
    else if (isEdit) {
      if (lateType) updateData.lateType = lateType;
      if (lateDate) updateData.lateDate = new Date(lateDate);
      if (estimatedTime) updateData.estimatedTime = estimatedTime;
      if (reason) updateData.reason = reason;
      if (attachmentUrl !== undefined) updateData.attachmentUrl = attachmentUrl || null;
      // Pastikan status tetap pending
      updateData.status = 'pending';
    }

    // Update late request
    const updatedLateRequest = await prisma.lateRequest.update({
      where: { id: parseInt(id) },
      data: updateData,
    });

    // Siapkan pesan respons
    const message = isStatusUpdate
      ? `Late request ${status} successfully`
      : 'Late request updated successfully';

    return NextResponse.json({
      message,
      lateRequest: updatedLateRequest,
    });
  } catch (error) {
    console.error('Error updating late request:', error);
    return NextResponse.json(
      { error: 'Failed to update late request' },
      { status: 500 }
    );
  }
}
